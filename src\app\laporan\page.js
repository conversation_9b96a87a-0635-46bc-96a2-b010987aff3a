'use client';

import { FileText, TrendingUp, Calendar, BarChart3 } from 'lucide-react';

export default function LaporanPage() {
  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <FileText className="w-8 h-8 mr-3 text-indigo-600" />
            Laporan & Statistik
          </h1>
          <p className="text-gray-600 mt-1">Laporan penjualan dan analisis bisnis</p>
        </div>

        {/* Coming Soon */}
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="w-24 h-24 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-12 h-12 text-indigo-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Laporan & Analytics</h2>
          <p className="text-gray-600 mb-6">
            Dashboard analisis bisnis, laporan penjualan, statistik member, dan performa station.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
            <div className="bg-gray-50 rounded-lg p-4">
              <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Penjualan</h3>
              <p className="text-sm text-gray-600">Laporan omzet harian/bulanan</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <BarChart3 className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Station</h3>
              <p className="text-sm text-gray-600">Performa setiap station</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <Calendar className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Periode</h3>
              <p className="text-sm text-gray-600">Filter berdasarkan tanggal</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <FileText className="w-8 h-8 text-orange-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Export</h3>
              <p className="text-sm text-gray-600">PDF, Excel, CSV</p>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            🚧 Halaman ini sedang dalam pengembangan
          </div>
        </div>
      </div>
    </div>
  );
}
