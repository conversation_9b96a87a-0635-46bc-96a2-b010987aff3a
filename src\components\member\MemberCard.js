'use client';

import { useState } from 'react';
import { User, MessageCircle, CreditCard, Edit, Trash2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import Button from '@/components/ui/Button';

const MemberCard = ({ 
  member, 
  onEdit, 
  onDelete, 
  onTopup,
  onViewHistory 
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleEdit = async () => {
    setIsProcessing(true);
    try {
      await onEdit(member);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm(`Hapus member ${member.nama}?`)) {
      setIsProcessing(true);
      try {
        await onDelete(member.id);
      } finally {
        setIsProcessing(false);
      }
    }
  };

  const handleTopup = async () => {
    setIsProcessing(true);
    try {
      await onTopup(member);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleViewHistory = () => {
    onViewHistory(member);
  };

  const getSaldoColor = (saldo) => {
    if (saldo >= 50000) return 'text-green-600';
    if (saldo >= 20000) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="card-member group hover:shadow-lg transition-all duration-300">
      {/* Member ID */}
      <div className="text-lg font-bold text-gray-900 mb-2">
        {member.id_member}
      </div>

      {/* Member Name */}
      <div className="flex items-center text-gray-700 mb-2">
        <User className="w-4 h-4 mr-2 text-gray-500" />
        <span className="truncate">{member.nama}</span>
      </div>

      {/* WhatsApp Number */}
      <div className="flex items-center text-gray-600 mb-3">
        <MessageCircle className="w-4 h-4 mr-2 text-green-500" />
        <span className="text-sm">{member.no_wa}</span>
      </div>

      {/* Balance */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <CreditCard className="w-4 h-4 mr-2 text-blue-500" />
          <span className="text-sm text-gray-600">Saldo:</span>
        </div>
        <span className={`font-bold ${getSaldoColor(member.saldo)}`}>
          {formatCurrency(member.saldo)}
        </span>
      </div>

      {/* Stats */}
      {member.total_topup > 0 && (
        <div className="text-xs text-gray-500 mb-3 p-2 bg-gray-50 rounded">
          <div className="flex justify-between">
            <span>Total Top-up:</span>
            <span className="font-medium">{formatCurrency(member.total_topup)}</span>
          </div>
          <div className="flex justify-between mt-1">
            <span>Riwayat:</span>
            <span className="font-medium">{member.riwayat?.length || 0} transaksi</span>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2 mt-auto">
        <Button
          variant="outline"
          size="sm"
          onClick={handleTopup}
          disabled={isProcessing}
          className="flex-1"
        >
          <CreditCard className="w-3 h-3 mr-1" />
          Top-up
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleEdit}
          disabled={isProcessing}
          className="px-2"
        >
          <Edit className="w-3 h-3" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDelete}
          disabled={isProcessing}
          className="px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <Trash2 className="w-3 h-3" />
        </Button>
      </div>

      {/* View History Button */}
      {(member.riwayat?.length > 0 || member.riwayat_penggunaan?.length > 0) && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleViewHistory}
          className="w-full mt-2 text-xs"
        >
          Lihat Riwayat
        </Button>
      )}

      {/* Processing Overlay */}
      {isProcessing && (
        <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-lg">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      )}
    </div>
  );
};

export default MemberCard;
