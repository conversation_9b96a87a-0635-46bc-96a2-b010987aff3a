@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-dark: rgb(30, 5, 177);
  --primary-light: rgb(22, 148, 233);
  --bg-light: rgb(222, 218, 236);
  --bg-dark: rgb(143, 150, 238);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* PlaySphere Custom Components */
.card-konsol {
  position: relative;
  background: linear-gradient(135deg, rgb(37, 99, 235), rgb(147, 51, 234));
  color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-konsol:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-konsol.active {
  background: linear-gradient(135deg, rgb(34, 197, 94), rgb(16, 185, 129));
  animation: pulse 2s infinite;
}

.card-member {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  border: 1px solid rgb(229, 231, 235);
  transition: box-shadow 0.3s ease;
}

.card-member:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* New Station Card Styles */
.station-card {
  width: 150px;
  height: 200px;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  color: white;
  margin-bottom: 8px; /* Add margin to prevent overlap */
  z-index: 1; /* Ensure proper stacking */
}

.station-card.idle {
  background: linear-gradient(135deg, #64748b, #475569);
}

.station-card.active {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  animation: pulse-glow 2s infinite;
}

.station-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  z-index: 10; /* Ensure hovered card is on top */
}

/* Special styling for Station 4 with DAMAR indicator */
.station-card.active.member-session {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 4px 20px rgba(34, 197, 94, 0.4);
  }
}

/* SweetAlert2 custom styles */
.colored-toast {
  border-radius: 0.5rem !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  outline: none;
  border: none;
  cursor: pointer;
}

.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.btn-primary {
  background-color: rgb(59, 130, 246);
  color: white;
}

.btn-primary:hover {
  background-color: rgb(37, 99, 235);
}

.btn-secondary {
  background-color: rgb(107, 114, 128);
  color: white;
}

.btn-secondary:hover {
  background-color: rgb(75, 85, 99);
}

.form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209, 213, 219);
  border-radius: 0.375rem;
  outline: none;
  transition: all 0.2s ease;
}

.form-control:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-color: transparent;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55, 65, 81);
  margin-bottom: 0.25rem;
}
