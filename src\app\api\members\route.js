import { NextResponse } from 'next/server';
import { findMany, insertRecord } from '@/lib/db';
import { generateMemberId } from '@/lib/utils';

// Get all members with transaction history (equivalent to Member::index in CI4)
export async function GET() {
  try {
    // Get all members
    const members = await findMany(`
      SELECT id, id_member, nama, no_wa, saldo, created_at, updated_at 
      FROM member 
      ORDER BY nama ASC
    `);

    // Get top-up history for each member
    for (let member of members) {
      const topupHistory = await findMany(`
        SELECT id, jumlah, metode_bayar, tanggal_topup, created_at
        FROM topup 
        WHERE id_member = ? 
        ORDER BY created_at DESC
      `, [member.id]);

      const usageHistory = await findMany(`
        SELECT 
          sesi.*, 
          station.nama_station, 
          konsol.nama_konsol, 
          konsol.harga_member, 
          paket.nama_paket, 
          paket.harga as harga_paket
        FROM sesi
        JOIN station ON station.id = sesi.station_id
        JOIN konsol ON konsol.id = station.id_konsol
        LEFT JOIN paket ON paket.id = sesi.id_paket
        WHERE sesi.id_member = ? AND sesi.harga_total > 0
        ORDER BY sesi.created_at DESC
      `, [member.id]);

      // Calculate total top-up
      const totalTopup = topupHistory.reduce((sum, topup) => sum + topup.jumlah, 0);

      member.riwayat = topupHistory;
      member.riwayat_penggunaan = usageHistory;
      member.total_topup = totalTopup;
    }

    return NextResponse.json({
      success: true,
      data: members
    });

  } catch (error) {
    console.error('Get members API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch members',
        error: error.message 
      },
      { status: 500 }
    );
  }
}

// Create new member (equivalent to Member::simpan in CI4)
export async function POST(request) {
  try {
    const body = await request.json();
    const { nama, no_wa } = body;

    // Validate required fields
    if (!nama || !no_wa) {
      return NextResponse.json(
        { success: false, message: 'Nama and WhatsApp number are required' },
        { status: 400 }
      );
    }

    // Generate member ID (PS + timestamp format like CI4)
    const id_member = generateMemberId();

    // Insert new member
    const memberId = await insertRecord('member', {
      id_member: id_member,
      nama: nama,
      no_wa: no_wa,
      saldo: 0,
      created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
    });

    return NextResponse.json({
      success: true,
      message: 'Member berhasil ditambahkan',
      data: {
        id: memberId,
        id_member: id_member,
        nama: nama,
        no_wa: no_wa,
        saldo: 0
      }
    });

  } catch (error) {
    console.error('Create member API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to create member',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
