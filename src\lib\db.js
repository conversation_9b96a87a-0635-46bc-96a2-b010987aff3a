import mysql from 'mysql2/promise';

// Database configuration based on CI4 config
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'playsphere',
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4',
  timezone: '+00:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// Create connection pool for better performance
let pool;

export function getPool() {
  if (!pool) {
    pool = mysql.createPool({
      ...dbConfig,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
  }
  return pool;
}

// Execute query with error handling
export async function executeQuery(query, params = []) {
  try {
    const pool = getPool();
    const [results] = await pool.execute(query, params);
    return results;
  } catch (error) {
    console.error('Database query error:', error);
    throw new Error(`Database error: ${error.message}`);
  }
}

// Get single record
export async function findOne(query, params = []) {
  const results = await executeQuery(query, params);
  return results[0] || null;
}

// Get multiple records
export async function findMany(query, params = []) {
  return await executeQuery(query, params);
}

// Insert record and return inserted ID
export async function insertRecord(table, data) {
  const fields = Object.keys(data);
  const values = Object.values(data);
  const placeholders = fields.map(() => '?').join(', ');
  
  const query = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
  const result = await executeQuery(query, values);
  return result.insertId;
}

// Update record
export async function updateRecord(table, data, whereClause, whereParams = []) {
  const fields = Object.keys(data);
  const values = Object.values(data);
  const setClause = fields.map(field => `${field} = ?`).join(', ');
  
  const query = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
  const result = await executeQuery(query, [...values, ...whereParams]);
  return result.affectedRows;
}

// Delete record
export async function deleteRecord(table, whereClause, whereParams = []) {
  const query = `DELETE FROM ${table} WHERE ${whereClause}`;
  const result = await executeQuery(query, whereParams);
  return result.affectedRows;
}

// Test database connection
export async function testConnection() {
  try {
    const pool = getPool();
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}

export default {
  executeQuery,
  findOne,
  findMany,
  insertRecord,
  updateRecord,
  deleteRecord,
  testConnection
};
