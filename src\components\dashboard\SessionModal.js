'use client';

import { useState, useEffect } from 'react';
import { User, Users, Package, CreditCard } from 'lucide-react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { dashboardApi } from '@/lib/api';
import { formatCurrency } from '@/lib/utils';
import { showError, showLoading, closeLoading } from '@/lib/sweetAlert';

const SessionModal = ({ 
  isOpen, 
  onClose, 
  station, 
  onSessionStart 
}) => {
  const [sessionType, setSessionType] = useState('personal');
  const [selectedMember, setSelectedMember] = useState('');
  const [selectedPackage, setSelectedPackage] = useState('');
  const [members, setMembers] = useState([]);
  const [packages, setPackages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch members and packages when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchData();
    }
  }, [isOpen]);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      
      const [membersResponse, packagesResponse] = await Promise.all([
        dashboardApi.getAllMembers(),
        dashboardApi.getAllPackages()
      ]);

      if (membersResponse.success) {
        setMembers(membersResponse.members);
      }

      if (packagesResponse.success) {
        // Filter packages for current console
        const filteredPackages = packagesResponse.pakets.filter(
          pkg => pkg.id_konsol === station?.id_konsol
        );
        setPackages(filteredPackages);
      }

    } catch (error) {
      console.error('Error fetching data:', error);
      showError('Error', 'Gagal memuat data member dan paket');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      // Validation
      if (sessionType === 'member' && !selectedMember) {
        showError('Error', 'Pilih member terlebih dahulu');
        return;
      }

      if (sessionType === 'paket' && (!selectedMember || !selectedPackage)) {
        showError('Error', 'Pilih member dan paket terlebih dahulu');
        return;
      }

      // Prepare session data
      const sessionData = {
        station_id: station.id,
        jenis_user: sessionType === 'paket' ? 'member' : sessionType,
        id_member: (sessionType === 'member' || sessionType === 'paket') ? selectedMember : null,
        id_paket: sessionType === 'paket' ? selectedPackage : null
      };

      showLoading('Memulai Sesi...', 'Mohon tunggu sebentar');

      const response = await dashboardApi.startSession(sessionData);
      
      closeLoading();

      if (response.success) {
        onSessionStart(response.data);
        onClose();
        resetForm();
      } else {
        showError('Error', response.message || 'Gagal memulai sesi');
      }

    } catch (error) {
      closeLoading();
      console.error('Error starting session:', error);
      showError('Error', error.message || 'Gagal memulai sesi');
    }
  };

  const resetForm = () => {
    setSessionType('personal');
    setSelectedMember('');
    setSelectedPackage('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const getSelectedMemberData = () => {
    return members.find(m => m.id === parseInt(selectedMember));
  };

  const getSelectedPackageData = () => {
    return packages.find(p => p.id === parseInt(selectedPackage));
  };

  if (!station) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`Mulai Sesi - ${station.nama_station}`}
      size="lg"
    >
      <div className="space-y-6">
        {/* Station Info */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">{station.nama_station}</h4>
          <p className="text-blue-700 text-sm">{station.nama_konsol}</p>
          <div className="mt-2 text-sm text-blue-600">
            <span>Personal: {formatCurrency(station.harga_personal)}/jam</span>
            <span className="ml-4">Member: {formatCurrency(station.harga_member)}/jam</span>
          </div>
        </div>

        {/* Session Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Pilih Jenis Sesi
          </label>
          <div className="grid grid-cols-3 gap-3">
            {/* Personal */}
            <button
              type="button"
              onClick={() => setSessionType('personal')}
              className={`p-4 border-2 rounded-lg transition-all ${
                sessionType === 'personal'
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <User className="w-8 h-8 mx-auto mb-2" />
              <div className="font-medium">Personal</div>
              <div className="text-xs text-gray-500">Bayar per jam</div>
            </button>

            {/* Member */}
            <button
              type="button"
              onClick={() => setSessionType('member')}
              className={`p-4 border-2 rounded-lg transition-all ${
                sessionType === 'member'
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <Users className="w-8 h-8 mx-auto mb-2" />
              <div className="font-medium">Member</div>
              <div className="text-xs text-gray-500">Potong saldo</div>
            </button>

            {/* Package */}
            <button
              type="button"
              onClick={() => setSessionType('paket')}
              className={`p-4 border-2 rounded-lg transition-all ${
                sessionType === 'paket'
                  ? 'border-purple-500 bg-purple-50 text-purple-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <Package className="w-8 h-8 mx-auto mb-2" />
              <div className="font-medium">Paket</div>
              <div className="text-xs text-gray-500">Durasi tetap</div>
            </button>
          </div>
        </div>

        {/* Member Selection */}
        {(sessionType === 'member' || sessionType === 'paket') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Pilih Member
            </label>
            {isLoading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : (
              <select
                value={selectedMember}
                onChange={(e) => setSelectedMember(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">-- Pilih Member --</option>
                {members.map((member) => (
                  <option key={member.id} value={member.id}>
                    {member.nama} ({member.id_member}) - Saldo: {formatCurrency(member.saldo)}
                  </option>
                ))}
              </select>
            )}
            
            {/* Selected Member Info */}
            {selectedMember && (
              <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{getSelectedMemberData()?.nama}</div>
                    <div className="text-sm text-gray-600">{getSelectedMemberData()?.id_member}</div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center text-green-600">
                      <CreditCard className="w-4 h-4 mr-1" />
                      {formatCurrency(getSelectedMemberData()?.saldo || 0)}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Package Selection */}
        {sessionType === 'paket' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Pilih Paket
            </label>
            {isLoading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : (
              <select
                value={selectedPackage}
                onChange={(e) => setSelectedPackage(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={!selectedMember}
              >
                <option value="">-- Pilih Paket --</option>
                {packages.map((pkg) => (
                  <option key={pkg.id} value={pkg.id}>
                    {pkg.nama_paket} - {pkg.durasi} menit - {formatCurrency(pkg.harga)}
                  </option>
                ))}
              </select>
            )}

            {/* Selected Package Info */}
            {selectedPackage && (
              <div className="mt-2 p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{getSelectedPackageData()?.nama_paket}</div>
                    <div className="text-sm text-gray-600">{getSelectedPackageData()?.keterangan}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-purple-600">
                      {formatCurrency(getSelectedPackageData()?.harga || 0)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {getSelectedPackageData()?.durasi} menit
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleClose}
          >
            Batal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
          >
            Mulai Sesi
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SessionModal;
