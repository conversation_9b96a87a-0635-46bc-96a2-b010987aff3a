'use client';

import { Package, Clock, DollarSign } from 'lucide-react';

export default function PaketPage() {
  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Package className="w-8 h-8 mr-3 text-green-600" />
            Paket Rental
          </h1>
          <p className="text-gray-600 mt-1">Pengaturan paket rental dan promo</p>
        </div>

        {/* Coming Soon */}
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Package className="w-12 h-12 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Paket Management</h2>
          <p className="text-gray-600 mb-6">
            Kelola paket rental dengan durasi tetap, harga khusus, dan promo menarik untuk member.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div className="bg-gray-50 rounded-lg p-4">
              <Clock className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Durasi</h3>
              <p className="text-sm text-gray-600">30 menit, 1 jam, 2 jam, dst</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Harga</h3>
              <p className="text-sm text-gray-600">Harga paket lebih hemat</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <Package className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Promo</h3>
              <p className="text-sm text-gray-600">Paket promo dan diskon</p>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            🚧 Halaman ini sedang dalam pengembangan
          </div>
        </div>
      </div>
    </div>
  );
}
