'use client';

import { Shopping<PERSON>art, Clock, CreditCard } from 'lucide-react';

export default function KasirPage() {
  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <ShoppingCart className="w-8 h-8 mr-3 text-blue-600" />
            Kasir (POS)
          </h1>
          <p className="text-gray-600 mt-1">Point of Sale - Pembayaran dan Transaksi</p>
        </div>

        {/* Coming Soon */}
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <ShoppingCart className="w-12 h-12 text-blue-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Kasir POS</h2>
          <p className="text-gray-600 mb-6">
            Halaman kasir untuk mengelola pembayaran sesi gaming, transaksi member, dan laporan penjualan.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div className="bg-gray-50 rounded-lg p-4">
              <Clock className="w-8 h-8 text-orange-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Sesi Aktif</h3>
              <p className="text-sm text-gray-600">Monitor sesi yang sedang berjalan</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <CreditCard className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Pembayaran</h3>
              <p className="text-sm text-gray-600">Proses checkout dan pembayaran</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <ShoppingCart className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Transaksi</h3>
              <p className="text-sm text-gray-600">Riwayat dan laporan transaksi</p>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            🚧 Halaman ini sedang dalam pengembangan
          </div>
        </div>
      </div>
    </div>
  );
}
