'use client';

import { useState } from 'react';
import { Wifi, Play, Square, ArrowRightLeft } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { CountdownTimer, ElapsedTimer } from '@/components/ui/Timer';

const StationCard = ({
  station,
  onStartSession,
  onStopSession,
  onMoveStation,
  isLoading = false
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const isActive = station.status === 'berjalan';

  const handleStartClick = () => {
    onStartSession(station);
  };

  const handleStopClick = async () => {
    if (!station.session_id) return;
    
    setIsProcessing(true);
    try {
      await onStopSession(station.session_id);
    } finally {
      setIsProcessing(false);
    }
  };

  const renderTimer = () => {
    if (!isActive) return '00:00:00';

    if (station.jenis_user === 'member' && station.nama_paket && station.durasi_sisa > 0) {
      // Package session with countdown
      return (
        <CountdownTimer
          durationInSeconds={station.durasi_sisa}
          isRunning={true}
          className="text-white"
        />
      );
    } else {
      // Personal or balance-based session with elapsed time
      return (
        <ElapsedTimer
          startTime={station.mulai}
          isRunning={true}
          className="text-white"
        />
      );
    }
  };

  const renderStatusInfo = () => {
    if (!isActive) {
      return (
        <div className="text-center">
          <div className="text-white/90 text-sm mb-1">PlaySphere</div>
          <div className="text-white/70 text-xs">Ready</div>
        </div>
      );
    }

    // For active sessions
    if (station.jenis_user === 'member' && station.nama_paket) {
      // Package session
      return (
        <div className="text-center">
          <div className="text-white font-semibold text-sm mb-1">
            {formatCurrency(station.harga_paket || 0)}
          </div>
          <div className="text-white/80 text-xs">
            {station.jenis_user === 'personal' ? 'Personal' : station.nama_member}
          </div>
        </div>
      );
    } else if (station.jenis_user === 'member') {
      // Member session with balance
      return (
        <div className="text-center">
          <div className="text-white font-semibold text-sm mb-1">
            {formatCurrency(station.member_saldo || 0)}
          </div>
          <div className="text-white/80 text-xs flex items-center justify-center">
            <span className="w-2 h-2 bg-yellow-400 rounded-full mr-1"></span>
            {station.nama_member || 'MEMBER'}
          </div>
        </div>
      );
    } else {
      // Personal session
      return (
        <div className="text-center">
          <div className="text-white font-semibold text-sm mb-1">
            {formatCurrency(station.harga_total || 0)}
          </div>
          <div className="text-white/80 text-xs">Personal</div>
        </div>
      );
    }
  };

  return (
    <div
      className={`station-card ${isActive ? 'active' : 'idle'} ${isProcessing ? 'opacity-75' : ''}`}
    >
      {/* Header with Station Name and WiFi */}
      <div className="flex items-center justify-between mb-2">
        <div className="text-white font-bold text-sm">
          {station.nama_station}
        </div>
        <Wifi className="w-4 h-4 text-white/70" />
      </div>

      {/* Console Type */}
      <div className="text-white/90 text-xs mb-3">
        {station.nama_konsol}
      </div>

      {/* Timer - Large Display */}
      <div className="text-center mb-4">
        <div className="text-white font-mono text-2xl font-bold tracking-wider">
          {renderTimer()}
        </div>
      </div>

      {/* Status Info */}
      <div className="mb-4">
        {renderStatusInfo()}
      </div>

      {/* Action Buttons - Inside Card */}
      <div className="flex justify-center gap-2 mt-auto">
        {isActive ? (
          <>
            {/* Stop Button */}
            <button
              onClick={handleStopClick}
              disabled={isProcessing || isLoading}
              className="w-10 h-10 bg-red-500 hover:bg-red-600 disabled:opacity-50 rounded-lg flex items-center justify-center transition-colors shadow-md"
              title="Stop Session"
            >
              <Square className="w-5 h-5 text-white fill-white" />
            </button>

            {/* Move Station Button */}
            <button
              onClick={() => onMoveStation(station)}
              disabled={isProcessing || isLoading}
              className="w-10 h-10 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 rounded-lg flex items-center justify-center transition-colors shadow-md"
              title="Pindah Station"
            >
              <ArrowRightLeft className="w-5 h-5 text-white" />
            </button>
          </>
        ) : (
          /* Play Button */
          <button
            onClick={handleStartClick}
            disabled={isProcessing || isLoading}
            className="w-10 h-10 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 rounded-lg flex items-center justify-center transition-colors shadow-md"
            title="Mulai Session"
          >
            <Play className="w-5 h-5 text-white fill-white" />
          </button>
        )}
      </div>
    </div>
  );
};

export default StationCard;
