import Swal from 'sweetalert2';

// Custom SweetAlert configurations for PlaySphere
const swalConfig = {
  customClass: {
    confirmButton: 'btn btn-primary mx-2',
    cancelButton: 'btn btn-secondary mx-2',
    popup: 'rounded-lg shadow-xl',
    title: 'text-xl font-bold text-gray-800',
    content: 'text-gray-600'
  },
  buttonsStyling: false,
  showClass: {
    popup: 'animate__animated animate__fadeInDown animate__faster'
  },
  hideClass: {
    popup: 'animate__animated animate__fadeOutUp animate__faster'
  }
};

// Success notification
export const showSuccess = (title, text = '', timer = 3000) => {
  return Swal.fire({
    ...swalConfig,
    icon: 'success',
    title: title,
    text: text,
    timer: timer,
    timerProgressBar: true,
    showConfirmButton: false,
    toast: true,
    position: 'top-end',
    customClass: {
      popup: 'colored-toast'
    }
  });
};

// Error notification
export const showError = (title, text = '') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'error',
    title: title,
    text: text,
    confirmButtonText: 'OK',
    confirmButtonColor: '#dc3545'
  });
};

// Warning notification
export const showWarning = (title, text = '') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'warning',
    title: title,
    text: text,
    confirmButtonText: 'OK',
    confirmButtonColor: '#ffc107'
  });
};

// Info notification
export const showInfo = (title, text = '') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'info',
    title: title,
    text: text,
    confirmButtonText: 'OK',
    confirmButtonColor: '#0dcaf0'
  });
};

// Confirmation dialog
export const showConfirm = (title, text = '', confirmText = 'Ya', cancelText = 'Batal') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'question',
    title: title,
    text: text,
    showCancelButton: true,
    confirmButtonText: confirmText,
    cancelButtonText: cancelText,
    confirmButtonColor: '#198754',
    cancelButtonColor: '#6c757d',
    reverseButtons: true
  });
};

// Delete confirmation
export const showDeleteConfirm = (itemName = 'item ini') => {
  return Swal.fire({
    ...swalConfig,
    icon: 'warning',
    title: 'Hapus Data?',
    text: `Apakah Anda yakin ingin menghapus ${itemName}? Data yang dihapus tidak dapat dikembalikan.`,
    showCancelButton: true,
    confirmButtonText: 'Ya, Hapus!',
    cancelButtonText: 'Batal',
    confirmButtonColor: '#dc3545',
    cancelButtonColor: '#6c757d',
    reverseButtons: true
  });
};

// Loading dialog
export const showLoading = (title = 'Memproses...', text = 'Mohon tunggu sebentar') => {
  return Swal.fire({
    ...swalConfig,
    title: title,
    text: text,
    allowOutsideClick: false,
    allowEscapeKey: false,
    showConfirmButton: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });
};

// Close loading
export const closeLoading = () => {
  Swal.close();
};

// Input dialog
export const showInput = (title, inputPlaceholder = '', inputType = 'text', inputValue = '') => {
  return Swal.fire({
    ...swalConfig,
    title: title,
    input: inputType,
    inputPlaceholder: inputPlaceholder,
    inputValue: inputValue,
    showCancelButton: true,
    confirmButtonText: 'OK',
    cancelButtonText: 'Batal',
    confirmButtonColor: '#198754',
    cancelButtonColor: '#6c757d',
    inputValidator: (value) => {
      if (!value) {
        return 'Field ini tidak boleh kosong!';
      }
    }
  });
};

// Multiple inputs dialog
export const showMultipleInputs = (title, inputs) => {
  const inputsHtml = inputs.map(input => `
    <div class="mb-3 text-left">
      <label class="form-label text-sm font-medium text-gray-700">${input.label}</label>
      <input 
        id="${input.id}" 
        type="${input.type || 'text'}" 
        class="form-control w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
        placeholder="${input.placeholder || ''}"
        value="${input.value || ''}"
        ${input.required ? 'required' : ''}
      />
    </div>
  `).join('');

  return Swal.fire({
    ...swalConfig,
    title: title,
    html: `<div class="space-y-4">${inputsHtml}</div>`,
    showCancelButton: true,
    confirmButtonText: 'Simpan',
    cancelButtonText: 'Batal',
    confirmButtonColor: '#198754',
    cancelButtonColor: '#6c757d',
    focusConfirm: false,
    preConfirm: () => {
      const result = {};
      inputs.forEach(input => {
        const element = document.getElementById(input.id);
        result[input.id] = element.value;
        
        if (input.required && !element.value) {
          Swal.showValidationMessage(`${input.label} tidak boleh kosong!`);
          return false;
        }
      });
      return result;
    }
  });
};

// Toast notification (top-right corner)
export const showToast = (icon, title, timer = 3000) => {
  return Swal.fire({
    icon: icon,
    title: title,
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: timer,
    timerProgressBar: true,
    customClass: {
      popup: 'colored-toast'
    }
  });
};

// Session result dialog (for gaming sessions)
export const showSessionResult = (data) => {
  const { duration_minutes, final_price, station_name } = data;
  
  return Swal.fire({
    ...swalConfig,
    icon: 'success',
    title: 'Sesi Selesai!',
    html: `
      <div class="text-center space-y-2">
        <p class="text-lg font-semibold text-gray-800">${station_name}</p>
        <div class="bg-gray-50 rounded-lg p-4 space-y-2">
          <div class="flex justify-between">
            <span class="text-gray-600">Durasi:</span>
            <span class="font-semibold">${duration_minutes} menit</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Total Biaya:</span>
            <span class="font-bold text-green-600">Rp ${final_price.toLocaleString('id-ID')}</span>
          </div>
        </div>
      </div>
    `,
    confirmButtonText: 'OK',
    confirmButtonColor: '#198754'
  });
};

// Payment success dialog
export const showPaymentSuccess = (data) => {
  const { amount, method, change = 0 } = data;
  
  return Swal.fire({
    ...swalConfig,
    icon: 'success',
    title: 'Pembayaran Berhasil!',
    html: `
      <div class="text-center space-y-2">
        <div class="bg-green-50 rounded-lg p-4 space-y-2">
          <div class="flex justify-between">
            <span class="text-gray-600">Jumlah:</span>
            <span class="font-semibold">Rp ${amount.toLocaleString('id-ID')}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Metode:</span>
            <span class="font-semibold capitalize">${method}</span>
          </div>
          ${change > 0 ? `
          <div class="flex justify-between border-t pt-2">
            <span class="text-gray-600">Kembalian:</span>
            <span class="font-bold text-blue-600">Rp ${change.toLocaleString('id-ID')}</span>
          </div>
          ` : ''}
        </div>
      </div>
    `,
    confirmButtonText: 'OK',
    confirmButtonColor: '#198754'
  });
};

export default {
  showSuccess,
  showError,
  showWarning,
  showInfo,
  showConfirm,
  showDeleteConfirm,
  showLoading,
  closeLoading,
  showInput,
  showMultipleInputs,
  showToast,
  showSessionResult,
  showPaymentSuccess
};
