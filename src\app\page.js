'use client';

import { useState, useEffect } from 'react';
import { dashboardApi } from '@/lib/api';
import StationCard from '@/components/dashboard/StationCard';
import SessionModal from '@/components/dashboard/SessionModal';
import MoveStationModal from '@/components/dashboard/MoveStationModal';
import Button from '@/components/ui/Button';
import { RefreshCw } from 'lucide-react';
import { showSuccess, showError, showConfirm, showSessionResult } from '@/lib/sweetAlert';

export default function Dashboard() {
  const [stations, setStations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [sessionModal, setSessionModal] = useState({
    isOpen: false,
    station: null
  });
  const [moveModal, setMoveModal] = useState({
    isOpen: false,
    station: null
  });

  // Fetch stations data
  const fetchStations = async () => {
    try {
      setError(null);
      const response = await dashboardApi.getStations();
      if (response.success) {
        setStations(response.data);
      } else {
        setError(response.message || 'Failed to fetch stations');
      }
    } catch (err) {
      setError(err.message || 'Network error');
      console.error('Error fetching stations:', err);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchStations();
  };

  // Handle start session - open modal
  const handleStartSession = (station) => {
    setSessionModal({
      isOpen: true,
      station: station
    });
  };

  // Handle session start from modal
  const handleSessionStart = async (sessionData) => {
    try {
      await fetchStations(); // Refresh stations
      showSuccess('Sesi Dimulai!', `Sesi berhasil dimulai di ${sessionData.station_name}`);
    } catch (err) {
      showError('Error', err.message || 'Gagal memulai sesi');
      console.error('Error starting session:', err);
    }
  };

  // Handle move station - open modal
  const handleMoveStation = (station) => {
    setMoveModal({
      isOpen: true,
      station: station
    });
  };

  // Handle station move from modal
  const handleStationMove = async (moveData) => {
    try {
      await fetchStations(); // Refresh stations
      showSuccess(
        'Station Dipindah!',
        `Sesi berhasil dipindah dari ${moveData.from_station.name} ke ${moveData.to_station.name}`
      );
    } catch (err) {
      showError('Error', err.message || 'Gagal memindahkan station');
      console.error('Error moving station:', err);
    }
  };

  // Handle stop session
  const handleStopSession = async (sessionId) => {
    try {
      const result = await showConfirm(
        'Hentikan Sesi?',
        'Apakah Anda yakin ingin menghentikan sesi ini?',
        'Ya, Hentikan',
        'Batal'
      );

      if (result.isConfirmed) {
        const response = await dashboardApi.stopSession(sessionId);

        if (response.success) {
          await fetchStations(); // Refresh stations

          // Show session result
          showSessionResult({
            duration_minutes: response.data.duration_minutes,
            final_price: response.data.final_price,
            station_name: response.data.station_name || 'Station'
          });
        } else {
          showError('Error', response.message || 'Gagal menghentikan sesi');
        }
      }
    } catch (err) {
      showError('Error', err.message || 'Gagal menghentikan sesi');
      console.error('Error stopping session:', err);
    }
  };

  // Initial load and auto-refresh
  useEffect(() => {
    fetchStations();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchStations, 30000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading stations...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <Button onClick={fetchStations}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">PlaySphere Dashboard</h1>
            <p className="text-gray-600 mt-1">Monitor Station Gaming</p>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>
        </div>

        {/* Stations Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-7 gap-4 mb-8">
          {stations.map((station) => (
            <StationCard
              key={station.id}
              station={station}
              onStartSession={handleStartSession}
              onStopSession={handleStopSession}
              onMoveStation={handleMoveStation}
              isLoading={isRefreshing}
            />
          ))}
        </div>

        {/* Empty State */}
        {stations.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg mb-4">No stations found</p>
            <p className="text-gray-400">Make sure your database is properly configured</p>
          </div>
        )}

        {/* Stats */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Stations</h3>
            <p className="text-3xl font-bold text-blue-600">{stations.length}</p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Active Sessions</h3>
            <p className="text-3xl font-bold text-green-600">
              {stations.filter(s => s.status === 'berjalan').length}
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Available Stations</h3>
            <p className="text-3xl font-bold text-gray-600">
              {stations.filter(s => s.status === 'tidak_aktif').length}
            </p>
          </div>
        </div>
      </div>

      {/* Session Modal */}
      <SessionModal
        isOpen={sessionModal.isOpen}
        onClose={() => setSessionModal({ isOpen: false, station: null })}
        station={sessionModal.station}
        onSessionStart={handleSessionStart}
      />

      {/* Move Station Modal */}
      <MoveStationModal
        isOpen={moveModal.isOpen}
        onClose={() => setMoveModal({ isOpen: false, station: null })}
        currentStation={moveModal.station}
        onStationMove={handleStationMove}
      />
    </div>
  );
}
