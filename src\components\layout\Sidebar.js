'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  Users, 
  ShoppingCart, 
  Gamepad2, 
  Package, 
  Settings, 
  FileText,
  ChevronLeft,
  ChevronRight,
  LogOut,
  User
} from 'lucide-react';

const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();

  const menuItems = [
    {
      title: 'Dashboard',
      href: '/',
      icon: LayoutDashboard,
      description: 'Monitor Station Gaming'
    },
    {
      title: 'Member',
      href: '/member',
      icon: Users,
      description: 'Kelola Data Member'
    },
    {
      title: 'Kasir',
      href: '/kasir',
      icon: ShoppingCart,
      description: 'Point of Sale'
    },
    {
      title: 'Konsol',
      href: '/konsol',
      icon: Gamepad2,
      description: 'Manajemen Konsol & Station'
    },
    {
      title: 'Paket',
      href: '/paket',
      icon: Package,
      description: 'Pengaturan Paket Rental'
    },
    {
      title: 'Laporan',
      href: '/laporan',
      icon: FileText,
      description: 'Laporan & Statistik'
    },
    {
      title: 'Setting',
      href: '/setting',
      icon: Settings,
      description: 'Pengaturan Sistem'
    }
  ];

  const isActive = (href) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <div className={`bg-gradient-to-b from-blue-900 to-purple-900 text-white transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } min-h-screen flex flex-col shadow-xl`}>
      
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <Gamepad2 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold">PlaySphere</h1>
                <p className="text-xs text-white/70">PS Rental System</p>
              </div>
            </div>
          )}
          
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-lg hover:bg-white/10 transition-colors"
          >
            {isCollapsed ? (
              <ChevronRight className="w-5 h-5" />
            ) : (
              <ChevronLeft className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 group ${
                active 
                  ? 'bg-white/20 text-white shadow-lg' 
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
              }`}
            >
              <Icon className={`w-5 h-5 ${active ? 'text-white' : 'text-white/70 group-hover:text-white'}`} />
              
              {!isCollapsed && (
                <div className="flex-1">
                  <div className="font-medium">{item.title}</div>
                  <div className="text-xs text-white/60 group-hover:text-white/80">
                    {item.description}
                  </div>
                </div>
              )}
              
              {active && !isCollapsed && (
                <div className="w-2 h-2 bg-white rounded-full"></div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* User Info & Logout */}
      <div className="p-4 border-t border-white/10">
        {!isCollapsed ? (
          <div className="space-y-3">
            {/* User Info */}
            <div className="flex items-center space-x-3 p-3 bg-white/10 rounded-lg">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <User className="w-4 h-4" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium">Admin</div>
                <div className="text-xs text-white/60">Operator</div>
              </div>
            </div>
            
            {/* Logout Button */}
            <button className="w-full flex items-center space-x-3 p-3 text-white/80 hover:bg-red-500/20 hover:text-red-200 rounded-lg transition-colors">
              <LogOut className="w-5 h-5" />
              <span>Keluar</span>
            </button>
          </div>
        ) : (
          <div className="space-y-2">
            <button className="w-full p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <User className="w-5 h-5 mx-auto" />
            </button>
            <button className="w-full p-3 text-red-200 hover:bg-red-500/20 rounded-lg transition-colors">
              <LogOut className="w-5 h-5 mx-auto" />
            </button>
          </div>
        )}
      </div>

      {/* Footer */}
      {!isCollapsed && (
        <div className="p-4 text-center border-t border-white/10">
          <p className="text-xs text-white/50">
            v3.0 © 2024 PlaySphere
          </p>
          <p className="text-xs text-white/40">
            Powered by Next.js
          </p>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
