// API client utilities for frontend components
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';

class ApiError extends Error {
  constructor(message, status, data) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

// Generic API request function
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}/api${endpoint}`;
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      console.error('API Error:', response.status, data);
      throw new ApiError(
        data.message || 'API request failed',
        response.status,
        data
      );
    }

    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error', 0, { originalError: error.message });
  }
}

// Dashboard API functions
export const dashboardApi = {
  // Get all stations with current session status
  getStations: () => apiRequest('/dashboard/stations'),
  
  // Get session status for specific station or all stations
  getSessionStatus: (stationId = null) => {
    const endpoint = stationId 
      ? `/dashboard/sessions/status/${stationId}`
      : '/dashboard/sessions/status';
    return apiRequest(endpoint);
  },
  
  // Start new session
  startSession: (data) => apiRequest('/dashboard/sessions', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Stop session
  stopSession: (sessionId) => apiRequest(`/dashboard/sessions/${sessionId}`, {
    method: 'DELETE',
  }),

  // Move session to different station
  moveStation: (data) => apiRequest('/dashboard/sessions/move', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Get all members for session modal
  getAllMembers: () => apiRequest('/dashboard/members'),
  
  // Get all packages for session modal
  getAllPackages: () => apiRequest('/dashboard/packages'),
};

// Kasir API functions
export const kasirApi = {
  // Get unpaid sessions
  getUnpaidSessions: () => apiRequest('/kasir/sessions/unpaid'),
  
  // Process checkout
  processCheckout: (data) => apiRequest('/kasir/checkout', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Pay session
  paySession: (sessionId, paymentData) => apiRequest(`/kasir/sessions/${sessionId}/pay`, {
    method: 'POST',
    body: JSON.stringify(paymentData),
  }),
  
  // Get products
  getProducts: () => apiRequest('/kasir/products'),
};

// Member API functions
export const memberApi = {
  // Get all members
  getMembers: () => apiRequest('/members'),
  
  // Create new member
  createMember: (data) => apiRequest('/members', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Update member
  updateMember: (id, data) => apiRequest(`/members/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  
  // Delete member
  deleteMember: (id) => apiRequest(`/members/${id}`, {
    method: 'DELETE',
  }),
  
  // Top up member balance
  topupMember: (id, data) => apiRequest(`/members/${id}/topup`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Get member transaction history
  getMemberHistory: (id) => apiRequest(`/members/${id}/history`),
};

// Console API functions
export const konsolApi = {
  // Get all consoles
  getConsoles: () => apiRequest('/konsol'),
  
  // Create new console
  createConsole: (data) => apiRequest('/konsol', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Update console
  updateConsole: (id, data) => apiRequest(`/konsol/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  
  // Delete console
  deleteConsole: (id) => apiRequest(`/konsol/${id}`, {
    method: 'DELETE',
  }),
  
  // Get all stations
  getStations: () => apiRequest('/konsol/stations'),
  
  // Create new station
  createStation: (data) => apiRequest('/konsol/stations', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Update station
  updateStation: (id, data) => apiRequest(`/konsol/stations/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  
  // Delete station
  deleteStation: (id) => apiRequest(`/konsol/stations/${id}`, {
    method: 'DELETE',
  }),
};

// Package API functions
export const paketApi = {
  // Get all packages
  getPackages: () => apiRequest('/paket'),
  
  // Create new package
  createPackage: (data) => apiRequest('/paket', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Update package
  updatePackage: (id, data) => apiRequest(`/paket/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  
  // Delete package
  deletePackage: (id) => apiRequest(`/paket/${id}`, {
    method: 'DELETE',
  }),
};

export { ApiError };
export default {
  dashboardApi,
  kasirApi,
  memberApi,
  konsolApi,
  paketApi,
};
