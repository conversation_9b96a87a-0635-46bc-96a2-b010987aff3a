import { NextResponse } from 'next/server';
import { findMany } from '@/lib/db';

// Get all packages for session modal (equivalent to Dashboard::getAllPakets in CI4)
export async function GET() {
  try {
    const packages = await findMany(`
      SELECT paket.*, konsol.nama_konsol 
      FROM paket 
      JOIN konsol ON konsol.id = paket.id_konsol 
      ORDER BY paket.nama_paket ASC
    `);

    return NextResponse.json({
      success: true,
      pakets: packages
    });

  } catch (error) {
    console.error('Get packages API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch packages',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
