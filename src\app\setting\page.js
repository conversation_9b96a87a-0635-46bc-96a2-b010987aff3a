'use client';

import { Settings, Database, Shield, Bell } from 'lucide-react';

export default function SettingPage() {
  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Settings className="w-8 h-8 mr-3 text-gray-600" />
            Pengaturan Sistem
          </h1>
          <p className="text-gray-600 mt-1">Konfigurasi aplikasi dan sistem</p>
        </div>

        {/* Coming Soon */}
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Settings className="w-12 h-12 text-gray-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">System Settings</h2>
          <p className="text-gray-600 mb-6">
            Pengaturan aplikasi, konfigurasi database, manajemen user, dan notifikasi sistem.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
            <div className="bg-gray-50 rounded-lg p-4">
              <Database className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Database</h3>
              <p className="text-sm text-gray-600">Backup & restore data</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <Shield className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Security</h3>
              <p className="text-sm text-gray-600">User & permission</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <Bell className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Notifikasi</h3>
              <p className="text-sm text-gray-600">Alert & reminder</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <Settings className="w-8 h-8 text-gray-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">General</h3>
              <p className="text-sm text-gray-600">Pengaturan umum</p>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            🚧 Halaman ini sedang dalam pengembangan
          </div>
        </div>
      </div>
    </div>
  );
}
