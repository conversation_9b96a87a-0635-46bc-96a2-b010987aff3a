'use client';

import { useState, useEffect } from 'react';
import { formatDuration } from '@/lib/utils';

const Timer = ({ 
  initialSeconds = 0, 
  isRunning = false, 
  onTimeUp = null,
  className = '',
  showHours = true 
}) => {
  const [seconds, setSeconds] = useState(initialSeconds);

  useEffect(() => {
    setSeconds(initialSeconds);
  }, [initialSeconds]);

  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      setSeconds(prevSeconds => {
        const newSeconds = prevSeconds - 1;
        
        if (newSeconds <= 0) {
          if (onTimeUp) {
            onTimeUp();
          }
          return 0;
        }
        
        return newSeconds;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, onTimeUp]);

  const formatTime = (totalSeconds) => {
    if (showHours) {
      return formatDuration(Math.max(0, totalSeconds));
    } else {
      const minutes = Math.floor(Math.max(0, totalSeconds) / 60);
      const secs = Math.max(0, totalSeconds) % 60;
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  };

  return (
    <div className={`font-mono font-bold ${className}`}>
      {formatTime(seconds)}
    </div>
  );
};

// Countdown timer that counts down from a specific duration
export const CountdownTimer = ({ 
  durationInSeconds, 
  isRunning = false, 
  onTimeUp = null,
  className = '' 
}) => {
  return (
    <Timer
      initialSeconds={durationInSeconds}
      isRunning={isRunning}
      onTimeUp={onTimeUp}
      className={className}
    />
  );
};

// Elapsed time timer that counts up from start time
export const ElapsedTimer = ({ 
  startTime, 
  isRunning = false,
  className = '' 
}) => {
  const [elapsedSeconds, setElapsedSeconds] = useState(0);

  useEffect(() => {
    if (!startTime) return;
    
    const calculateElapsed = () => {
      const now = new Date();
      const start = new Date(startTime);
      const elapsed = Math.floor((now - start) / 1000);
      setElapsedSeconds(Math.max(0, elapsed));
    };

    calculateElapsed(); // Initial calculation

    if (!isRunning) return;

    const interval = setInterval(calculateElapsed, 1000);
    return () => clearInterval(interval);
  }, [startTime, isRunning]);

  return (
    <div className={`font-mono font-bold ${className}`}>
      {formatDuration(elapsedSeconds)}
    </div>
  );
};

export default Timer;
