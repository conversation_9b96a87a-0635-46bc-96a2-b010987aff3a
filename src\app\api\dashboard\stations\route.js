import { NextResponse } from 'next/server';
import { findMany } from '@/lib/db';

export async function GET() {
  try {
    // Get all stations with console info (equivalent to Dashboard::index in CI4)
    const stationsQuery = `
      SELECT 
        station.*, 
        konsol.nama_konsol, 
        konsol.harga_personal, 
        konsol.harga_member
      FROM station 
      JOIN konsol ON konsol.id = station.id_konsol
      ORDER BY station.id
    `;
    
    const stations = await findMany(stationsQuery);

    // Get running sessions with details (equivalent to SesiModel::getRunningSessionsWithDetails)
    const sessionsQuery = `
      SELECT 
        sesi.*, 
        member.nama as nama_member, 
        paket.nama_paket, 
        paket.durasi as durasi_paket, 
        konsol.nama_konsol, 
        station.nama_station, 
        konsol.harga_personal, 
        konsol.harga_member
      FROM sesi
      LEFT JOIN member ON member.id = sesi.id_member
      LEFT JOIN paket ON paket.id = sesi.id_paket
      JOIN station ON station.id = sesi.station_id
      JOIN konsol ON konsol.id = station.id_konsol
      WHERE sesi.status = 'berjalan'
    `;
    
    const runningSessions = await findMany(sessionsQuery);

    // Create session map for easy lookup
    const sessionMap = {};
    runningSessions.forEach(session => {
      // Calculate remaining duration for member sessions with packages
      if (session.jenis_user === 'member' && session.id_paket && session.durasi_paket) {
        const startTime = new Date(session.waktu_mulai);
        const now = new Date();
        const elapsedSeconds = Math.floor((now - startTime) / 1000);
        const totalDurationSeconds = session.durasi_paket * 60; // durasi in minutes
        session.durasi_sisa = Math.max(0, totalDurationSeconds - elapsedSeconds);
      } else {
        session.durasi_sisa = session.durasi_sisa || 0;
      }
      
      sessionMap[session.station_id] = session;
    });

    // Merge station data with session data
    const stationsWithSessions = stations.map(station => {
      const session = sessionMap[station.id];
      
      return {
        ...station,
        mulai: session?.waktu_mulai || null,
        status: session ? 'berjalan' : 'tidak_aktif',
        jenis_user: session?.jenis_user || null,
        nama_member: session?.nama_member || null,
        nama_paket: session?.nama_paket || null,
        durasi_sisa: session?.durasi_sisa || null,
        harga_total: session?.harga_total || 0,
        member_saldo: session?.sisa_saldo || 0,
        session_id: session?.id || null
      };
    });

    return NextResponse.json({
      success: true,
      data: stationsWithSessions
    });

  } catch (error) {
    console.error('Dashboard stations API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch stations data',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
