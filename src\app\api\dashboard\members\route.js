import { NextResponse } from 'next/server';
import { findMany } from '@/lib/db';

// Get all members for session modal (equivalent to Dashboard::getAllMembers in CI4)
export async function GET() {
  try {
    const members = await findMany(`
      SELECT id, id_member, nama, no_wa, saldo 
      FROM member 
      ORDER BY nama ASC
    `);

    return NextResponse.json({
      success: true,
      members: members
    });

  } catch (error) {
    console.error('Get members API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch members',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
