import { NextResponse } from 'next/server';
import { findOne, updateRecord } from '@/lib/db';

// Move session to different station
export async function POST(request) {
  try {
    const body = await request.json();
    const { session_id, from_station_id, to_station_id } = body;

    console.log('Moving session:', { session_id, from_station_id, to_station_id });

    // Validate required fields
    if (!session_id || !from_station_id || !to_station_id) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if session exists and is running
    const session = await findOne(`
      SELECT 
        sesi.*, 
        from_station.nama_station as from_station_name,
        from_station.id_konsol as from_konsol_id
      FROM sesi
      JOIN station as from_station ON from_station.id = sesi.station_id
      WHERE sesi.id = ? AND sesi.status = 'berjalan'
    `, [session_id]);

    if (!session) {
      return NextResponse.json(
        { success: false, message: 'Session not found or not running' },
        { status: 404 }
      );
    }

    // Check if target station exists and is available
    const targetStation = await findOne(`
      SELECT 
        station.*, 
        konsol.nama_konsol,
        konsol.id as konsol_id
      FROM station
      JOIN konsol ON konsol.id = station.id_konsol
      WHERE station.id = ?
    `, [to_station_id]);

    if (!targetStation) {
      return NextResponse.json(
        { success: false, message: 'Target station not found' },
        { status: 404 }
      );
    }

    // Check if target station has same console type
    if (targetStation.konsol_id !== session.from_konsol_id) {
      return NextResponse.json(
        { success: false, message: 'Target station must have same console type' },
        { status: 400 }
      );
    }

    // Check if target station is available
    const existingSession = await findOne(`
      SELECT id FROM sesi 
      WHERE station_id = ? AND status = 'berjalan'
    `, [to_station_id]);

    if (existingSession) {
      return NextResponse.json(
        { success: false, message: 'Target station is already in use' },
        { status: 400 }
      );
    }

    // Update session to new station
    const now = new Date();
    await updateRecord('sesi', {
      station_id: to_station_id,
      updated_at: now.toISOString().slice(0, 19).replace('T', ' ')
    }, 'id = ?', [session_id]);

    return NextResponse.json({
      success: true,
      message: 'Session moved successfully',
      data: {
        session_id: session_id,
        from_station: {
          id: from_station_id,
          name: session.from_station_name
        },
        to_station: {
          id: to_station_id,
          name: targetStation.nama_station
        }
      }
    });

  } catch (error) {
    console.error('Move station API error:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to move station',
        error: error.message,
        details: error.stack
      },
      { status: 500 }
    );
  }
}
