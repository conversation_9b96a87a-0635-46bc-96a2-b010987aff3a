import { NextResponse } from 'next/server';
import { findMany, insertRecord, findOne } from '@/lib/db';

// Start new session (equivalent to Dashboard::mulai in CI4)
export async function POST(request) {
  try {
    const body = await request.json();
    const { station_id, jenis_user, id_member, id_paket } = body;

    // Validate required fields
    if (!station_id || !jenis_user) {
      return NextResponse.json(
        { success: false, message: 'Station ID and user type are required' },
        { status: 400 }
      );
    }

    // Check if station is already active
    const existingSession = await findOne(
      'SELECT id FROM sesi WHERE station_id = ? AND status = "berjalan"',
      [station_id]
    );

    if (existingSession) {
      return NextResponse.json(
        { success: false, message: 'Station sudah aktif' },
        { status: 400 }
      );
    }

    // Get station and console info
    const station = await findOne(`
      SELECT station.*, konsol.harga_personal, konsol.harga_member 
      FROM station 
      JOIN konsol ON konsol.id = station.id_konsol 
      WHERE station.id = ?
    `, [station_id]);

    if (!station) {
      return NextResponse.json(
        { success: false, message: 'Station not found' },
        { status: 404 }
      );
    }

    let hargaAwal = 0;
    let durasiSisa = 0;
    let sisaSaldo = 0;

    if (jenis_user === 'personal') {
      hargaAwal = station.harga_personal;
    } else if (jenis_user === 'member') {
      if (id_paket) {
        // Package-based session
        const paket = await findOne('SELECT * FROM paket WHERE id = ?', [id_paket]);
        if (!paket) {
          return NextResponse.json(
            { success: false, message: 'Package not found' },
            { status: 404 }
          );
        }
        hargaAwal = paket.harga;
        durasiSisa = paket.durasi * 60; // Convert minutes to seconds
      } else {
        // Balance-based session
        if (!id_member) {
          return NextResponse.json(
            { success: false, message: 'Member ID required for member session' },
            { status: 400 }
          );
        }
        
        const member = await findOne('SELECT * FROM member WHERE id = ?', [id_member]);
        if (!member) {
          return NextResponse.json(
            { success: false, message: 'Member not found' },
            { status: 404 }
          );
        }
        
        if (member.saldo <= 0) {
          return NextResponse.json(
            { success: false, message: 'Saldo member tidak mencukupi' },
            { status: 400 }
          );
        }
        
        sisaSaldo = member.saldo;
        hargaAwal = 0; // Will be calculated based on time
      }
    }

    // Create session data
    const sessionData = {
      station_id: station_id,
      jenis_user: jenis_user,
      waktu_mulai: new Date().toISOString().slice(0, 19).replace('T', ' '),
      harga_total: hargaAwal,
      status: 'berjalan',
      id_member: id_member || null,
      id_paket: id_paket || null,
      durasi_sisa: durasiSisa,
      sisa_saldo: sisaSaldo,
      created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
    };

    // Insert session
    const sessionId = await insertRecord('sesi', sessionData);

    return NextResponse.json({
      success: true,
      message: 'Sesi dimulai',
      data: {
        session_id: sessionId,
        station_id: station_id,
        station_name: station.nama_station,
        harga_awal: hargaAwal
      }
    });

  } catch (error) {
    console.error('Start session API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to start session',
        error: error.message 
      },
      { status: 500 }
    );
  }
}

// Get session status (equivalent to Dashboard::getSessionStatus in CI4)
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const stationId = searchParams.get('station_id');

    if (stationId) {
      // Get specific station session
      const session = await findOne(`
        SELECT 
          sesi.*, 
          member.nama as nama_member, 
          member.saldo as member_saldo,
          paket.nama_paket, 
          paket.durasi as durasi_paket, 
          konsol.nama_konsol, 
          station.nama_station, 
          konsol.harga_personal, 
          konsol.harga_member
        FROM sesi
        LEFT JOIN member ON member.id = sesi.id_member
        LEFT JOIN paket ON paket.id = sesi.id_paket
        JOIN station ON station.id = sesi.station_id
        JOIN konsol ON konsol.id = station.id_konsol
        WHERE sesi.station_id = ? AND sesi.status = 'berjalan'
      `, [stationId]);

      if (!session) {
        return NextResponse.json({
          success: true,
          session: null
        });
      }

      // Calculate remaining duration for package sessions
      if (session.jenis_user === 'member' && session.id_paket && session.durasi_paket) {
        const startTime = new Date(session.waktu_mulai);
        const now = new Date();
        const elapsedSeconds = Math.floor((now - startTime) / 1000);
        const totalDurationSeconds = session.durasi_paket * 60;
        session.durasi_sisa = Math.max(0, totalDurationSeconds - elapsedSeconds);
      }

      return NextResponse.json({
        success: true,
        session: session
      });

    } else {
      // Get all running sessions
      const sessions = await findMany(`
        SELECT 
          sesi.*, 
          member.nama as nama_member, 
          member.saldo as member_saldo,
          paket.nama_paket, 
          paket.durasi as durasi_paket, 
          konsol.nama_konsol, 
          station.nama_station, 
          konsol.harga_personal, 
          konsol.harga_member
        FROM sesi
        LEFT JOIN member ON member.id = sesi.id_member
        LEFT JOIN paket ON paket.id = sesi.id_paket
        JOIN station ON station.id = sesi.station_id
        JOIN konsol ON konsol.id = station.id_konsol
        WHERE sesi.status = 'berjalan'
        ORDER BY sesi.waktu_mulai DESC
      `);

      // Calculate remaining duration for each session
      sessions.forEach(session => {
        if (session.jenis_user === 'member' && session.id_paket && session.durasi_paket) {
          const startTime = new Date(session.waktu_mulai);
          const now = new Date();
          const elapsedSeconds = Math.floor((now - startTime) / 1000);
          const totalDurationSeconds = session.durasi_paket * 60;
          session.durasi_sisa = Math.max(0, totalDurationSeconds - elapsedSeconds);
        }
      });

      return NextResponse.json({
        success: true,
        sessions: sessions,
        count: sessions.length
      });
    }

  } catch (error) {
    console.error('Get session status API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get session status',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
