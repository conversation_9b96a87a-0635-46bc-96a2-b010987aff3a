import { NextResponse } from 'next/server';
import { testConnection, findMany } from '@/lib/db';

export async function GET() {
  try {
    // Test database connection
    const isConnected = await testConnection();
    
    if (!isConnected) {
      return NextResponse.json(
        { success: false, message: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Test query - get station count
    const stations = await findMany('SELECT COUNT(*) as count FROM station');
    const members = await findMany('SELECT COUNT(*) as count FROM member');
    const sessions = await findMany('SELECT COUNT(*) as count FROM sesi WHERE status = "berjalan"');

    // Get sample active session
    const sampleSession = await findMany('SELECT * FROM sesi WHERE status = "berjalan" LIMIT 1');

    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      data: {
        stations: stations[0]?.count || 0,
        members: members[0]?.count || 0,
        activeSessions: sessions[0]?.count || 0,
        sampleSession: sampleSession[0] || null
      }
    });

  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Database test failed',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
