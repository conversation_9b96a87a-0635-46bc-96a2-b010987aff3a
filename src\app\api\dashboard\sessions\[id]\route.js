import { NextResponse } from 'next/server';
import { findOne, updateRecord } from '@/lib/db';

// Stop session (equivalent to Dashboard::stop in CI4)
export async function DELETE(_request, { params }) {
  try {
    const sessionId = params.id;
    console.log('Stopping session:', sessionId);

    // Get session details
    console.log('Fetching session details for ID:', sessionId);
    const session = await findOne(`
      SELECT
        sesi.*,
        member.saldo as member_saldo,
        konsol.harga_member,
        konsol.harga_personal,
        station.nama_station
      FROM sesi
      LEFT JOIN member ON member.id = sesi.id_member
      LEFT JOIN station ON station.id = sesi.station_id
      LEFT JOIN konsol ON konsol.id = station.id_konsol
      WHERE sesi.id = ? AND sesi.status = 'berjalan'
    `, [sessionId]);

    console.log('Session found:', session);

    if (!session) {
      return NextResponse.json(
        { success: false, message: 'Session not found or already stopped' },
        { status: 404 }
      );
    }

    const now = new Date();
    const startTime = new Date(session.waktu_mulai);
    const durationInSeconds = Math.floor((now - startTime) / 1000);
    const durationInMinutes = Math.ceil(durationInSeconds / 60);

    let finalPrice = session.harga_total;

    // Calculate final price for different session types
    if (session.jenis_user === 'personal') {
      // Personal session: calculate based on time
      const hourlyRate = session.harga_personal;
      finalPrice = Math.ceil(durationInMinutes / 60) * hourlyRate;
    } else if (session.jenis_user === 'member') {
      if (session.id_paket) {
        // Package session: use package price
        finalPrice = session.harga_total;
      } else {
        // Balance-based session: calculate based on time and member rate
        const memberRate = session.harga_member;
        const costPerMinute = memberRate / 60;
        finalPrice = Math.ceil(durationInMinutes * costPerMinute);
        
        // Update member balance
        if (session.id_member) {
          const newBalance = Math.max(0, session.member_saldo - finalPrice);
          await updateRecord(
            'member', 
            { saldo: newBalance }, 
            'id = ?', 
            [session.id_member]
          );
        }
      }
    }

    // Update session to stopped
    await updateRecord('sesi', {
      status: 'berhenti',
      waktu_berhenti: now.toISOString().slice(0, 19).replace('T', ' '),
      harga_total: finalPrice,
      updated_at: now.toISOString().slice(0, 19).replace('T', ' ')
    }, 'id = ?', [sessionId]);

    return NextResponse.json({
      success: true,
      message: 'Session stopped successfully',
      data: {
        session_id: sessionId,
        duration_minutes: durationInMinutes,
        final_price: finalPrice,
        station_name: session.nama_station
      }
    });

  } catch (error) {
    console.error('Stop session API error:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to stop session',
        error: error.message,
        details: error.stack
      },
      { status: 500 }
    );
  }
}

// Get specific session info
export async function GET(_request, { params }) {
  try {
    const sessionId = params.id;

    const session = await findOne(`
      SELECT 
        sesi.*, 
        member.nama as nama_member, 
        member.saldo as member_saldo,
        paket.nama_paket, 
        paket.durasi as durasi_paket, 
        konsol.nama_konsol, 
        station.nama_station, 
        konsol.harga_personal, 
        konsol.harga_member
      FROM sesi
      LEFT JOIN member ON member.id = sesi.id_member
      LEFT JOIN paket ON paket.id = sesi.id_paket
      JOIN station ON station.id = sesi.station_id
      JOIN konsol ON konsol.id = station.id_konsol
      WHERE sesi.id = ?
    `, [sessionId]);

    if (!session) {
      return NextResponse.json(
        { success: false, message: 'Session not found' },
        { status: 404 }
      );
    }

    // Calculate current duration and remaining time
    if (session.status === 'berjalan') {
      const startTime = new Date(session.waktu_mulai);
      const now = new Date();
      const elapsedSeconds = Math.floor((now - startTime) / 1000);
      
      session.elapsed_seconds = elapsedSeconds;
      
      if (session.jenis_user === 'member' && session.id_paket && session.durasi_paket) {
        const totalDurationSeconds = session.durasi_paket * 60;
        session.durasi_sisa = Math.max(0, totalDurationSeconds - elapsedSeconds);
      }
    }

    return NextResponse.json({
      success: true,
      data: session
    });

  } catch (error) {
    console.error('Get session info API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get session info',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
