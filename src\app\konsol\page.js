'use client';

import { Gamepad2, Monitor, Settings } from 'lucide-react';

export default function KonsolPage() {
  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Gamepad2 className="w-8 h-8 mr-3 text-purple-600" />
            Konsol & Station
          </h1>
          <p className="text-gray-600 mt-1">Manajemen konsol gaming dan station</p>
        </div>

        {/* Coming Soon */}
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Gamepad2 className="w-12 h-12 text-purple-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Konsol Management</h2>
          <p className="text-gray-600 mb-6">
            Kelola data konsol gaming, station, harga rental, dan konfigurasi perangkat.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div className="bg-gray-50 rounded-lg p-4">
              <Gamepad2 className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Konsol</h3>
              <p className="text-sm text-gray-600">PS4, PS5, Xbox, Nintendo Switch</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <Monitor className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Station</h3>
              <p className="text-sm text-gray-600">Setup dan konfigurasi station</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <Settings className="w-8 h-8 text-gray-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Harga</h3>
              <p className="text-sm text-gray-600">Atur harga rental per jam</p>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            🚧 Halaman ini sedang dalam pengembangan
          </div>
        </div>
      </div>
    </div>
  );
}
