'use client';

import { useState, useEffect } from 'react';
import { ArrowRightLeft, Monitor } from 'lucide-react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { dashboardApi } from '@/lib/api';
import { showError, showLoading, closeLoading } from '@/lib/sweetAlert';

const MoveStationModal = ({ 
  isOpen, 
  onClose, 
  currentStation, 
  onStationMove 
}) => {
  const [availableStations, setAvailableStations] = useState([]);
  const [selectedStation, setSelectedStation] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Fetch available stations when modal opens
  useEffect(() => {
    if (isOpen && currentStation) {
      fetchAvailableStations();
    }
  }, [isOpen, currentStation]);

  const fetchAvailableStations = async () => {
    try {
      setIsLoading(true);
      
      const response = await dashboardApi.getStations();

      console.log('Move Station - API Response:', response);

      // Check if response has data array (from getStations API)
      const stationsData = response.stations || response.data || [];

      if (response.success && Array.isArray(stationsData)) {
        // Filter stations with same console type and not in use
        const filtered = stationsData.filter(station =>
          station.id_konsol === currentStation.id_konsol && // Same console type
          station.id !== currentStation.id && // Not current station
          station.status !== 'berjalan' // Not in use
        );

        console.log('Filtered available stations:', filtered);
        setAvailableStations(filtered);
      } else {
        console.error('Invalid stations data:', response);
        showError('Error', 'Gagal memuat daftar station atau data tidak valid');
        setAvailableStations([]);
      }
    } catch (error) {
      console.error('Error fetching available stations:', error);
      showError('Error', 'Gagal memuat daftar station');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMove = async () => {
    if (!selectedStation) {
      showError('Error', 'Pilih station tujuan terlebih dahulu');
      return;
    }

    try {
      showLoading('Memindahkan Station...', 'Mohon tunggu sebentar');

      const response = await dashboardApi.moveStation({
        session_id: currentStation.session_id,
        from_station_id: currentStation.id,
        to_station_id: parseInt(selectedStation)
      });

      closeLoading();

      if (response.success) {
        onStationMove(response.data);
        onClose();
        resetForm();
      } else {
        showError('Error', response.message || 'Gagal memindahkan station');
      }
    } catch (error) {
      closeLoading();
      console.error('Error moving station:', error);
      showError('Error', error.message || 'Gagal memindahkan station');
    }
  };

  const resetForm = () => {
    setSelectedStation('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!currentStation) return null;

  const selectedStationData = availableStations.find(s => s.id === parseInt(selectedStation));

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Pindah Station"
      size="default"
    >
      <div className="space-y-6">
        {/* Current Station Info */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Station Saat Ini</h4>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-blue-800">{currentStation.nama_station}</p>
              <p className="text-blue-600 text-sm">{currentStation.nama_konsol}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-blue-600">
                {currentStation.jenis_user === 'member' ? 'Member' : 'Personal'}
              </p>
              {currentStation.nama_member && (
                <p className="text-xs text-blue-500">{currentStation.nama_member}</p>
              )}
            </div>
          </div>
        </div>

        {/* Available Stations */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Pilih Station Tujuan ({currentStation.nama_konsol})
          </label>
          
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-gray-500">Memuat station...</p>
            </div>
          ) : availableStations.length === 0 ? (
            <div className="text-center py-8">
              <Monitor className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Tidak ada station {currentStation.nama_konsol} yang tersedia</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-3">
              {availableStations.map((station) => (
                <label
                  key={station.id}
                  className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    selectedStation === station.id.toString()
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="station"
                    value={station.id}
                    checked={selectedStation === station.id.toString()}
                    onChange={(e) => setSelectedStation(e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900">{station.nama_station}</h4>
                        <p className="text-sm text-gray-600">{station.nama_konsol}</p>
                      </div>
                      <div className="text-right">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Tersedia
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className={`w-4 h-4 rounded-full border-2 ml-3 ${
                    selectedStation === station.id.toString()
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedStation === station.id.toString() && (
                      <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                    )}
                  </div>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Selected Station Preview */}
        {selectedStationData && (
          <div className="bg-green-50 rounded-lg p-4">
            <h4 className="font-semibold text-green-900 mb-2">Station Tujuan</h4>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-green-800">{selectedStationData.nama_station}</p>
                <p className="text-green-600 text-sm">{selectedStationData.nama_konsol}</p>
              </div>
              <ArrowRightLeft className="w-6 h-6 text-green-600" />
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleClose}
          >
            Batal
          </Button>
          <Button
            onClick={handleMove}
            disabled={!selectedStation || isLoading}
          >
            <ArrowRightLeft className="w-4 h-4 mr-2" />
            Pindah Station
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default MoveStationModal;
