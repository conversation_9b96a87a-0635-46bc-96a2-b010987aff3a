'use client';

import { useState, useEffect } from 'react';
import { memberApi } from '@/lib/api';
import { debounce } from '@/lib/utils';
import MemberCard from '@/components/member/MemberCard';
import Button from '@/components/ui/Button';
import { UserPlus, Search, RefreshCw } from 'lucide-react';
import {
  showSuccess,
  showError,
  showDeleteConfirm,
  showMultipleInputs,
  showInput
} from '@/lib/sweetAlert';

export default function MemberPage() {
  const [members, setMembers] = useState([]);
  const [filteredMembers, setFilteredMembers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch members data
  const fetchMembers = async () => {
    try {
      setError(null);
      const response = await memberApi.getMembers();
      if (response.success) {
        setMembers(response.data);
        setFilteredMembers(response.data);
      } else {
        setError(response.message || 'Failed to fetch members');
      }
    } catch (err) {
      setError(err.message || 'Network error');
      console.error('Error fetching members:', err);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchMembers();
  };

  // Handle search with debounce
  const debouncedSearch = debounce((query) => {
    if (!query.trim()) {
      setFilteredMembers(members);
      return;
    }

    const filtered = members.filter(member =>
      member.nama.toLowerCase().includes(query.toLowerCase()) ||
      member.id_member.toLowerCase().includes(query.toLowerCase()) ||
      member.no_wa.includes(query)
    );
    setFilteredMembers(filtered);
  }, 300);

  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, members]);

  // Handle create member
  const handleCreateMember = async () => {
    try {
      const result = await showMultipleInputs('Tambah Member Baru', [
        {
          id: 'nama',
          label: 'Nama Member',
          type: 'text',
          placeholder: 'Masukkan nama member',
          required: true
        },
        {
          id: 'no_wa',
          label: 'Nomor WhatsApp',
          type: 'tel',
          placeholder: 'Contoh: 08123456789',
          required: true
        }
      ]);

      if (result.isConfirmed) {
        const response = await memberApi.createMember(result.value);
        if (response.success) {
          showSuccess('Berhasil!', 'Member berhasil ditambahkan');
          await fetchMembers();
        } else {
          showError('Error', response.message || 'Gagal menambahkan member');
        }
      }
    } catch (err) {
      showError('Error', err.message || 'Gagal menambahkan member');
      console.error('Error creating member:', err);
    }
  };

  // Handle edit member
  const handleEditMember = async (member) => {
    try {
      const result = await showMultipleInputs('Edit Member', [
        {
          id: 'nama',
          label: 'Nama Member',
          type: 'text',
          placeholder: 'Masukkan nama member',
          value: member.nama,
          required: true
        },
        {
          id: 'no_wa',
          label: 'Nomor WhatsApp',
          type: 'tel',
          placeholder: 'Contoh: 08123456789',
          value: member.no_wa,
          required: true
        }
      ]);

      if (result.isConfirmed) {
        const response = await memberApi.updateMember(member.id, result.value);
        if (response.success) {
          showSuccess('Berhasil!', 'Member berhasil diperbarui');
          await fetchMembers();
        } else {
          showError('Error', response.message || 'Gagal memperbarui member');
        }
      }
    } catch (err) {
      showError('Error', err.message || 'Gagal memperbarui member');
      console.error('Error updating member:', err);
    }
  };

  // Handle delete member
  const handleDeleteMember = async (memberId) => {
    try {
      const member = members.find(m => m.id === memberId);
      const result = await showDeleteConfirm(`member ${member?.nama || 'ini'}`);

      if (result.isConfirmed) {
        const response = await memberApi.deleteMember(memberId);
        if (response.success) {
          showSuccess('Berhasil!', 'Member berhasil dihapus');
          await fetchMembers();
        } else {
          showError('Error', response.message || 'Gagal menghapus member');
        }
      }
    } catch (err) {
      showError('Error', err.message || 'Gagal menghapus member');
      console.error('Error deleting member:', err);
    }
  };

  // Handle top-up
  const handleTopupMember = async (member) => {
    try {
      const result = await showInput(
        `Top-up Saldo - ${member.nama}`,
        'Masukkan jumlah top-up',
        'number',
        ''
      );

      if (result.isConfirmed) {
        const jumlah = parseInt(result.value);
        if (isNaN(jumlah) || jumlah <= 0) {
          showError('Error', 'Jumlah harus berupa angka positif');
          return;
        }

        const response = await memberApi.topupMember(member.id, {
          jumlah,
          metode_bayar: 'tunai'
        });

        if (response.success) {
          showSuccess(
            'Top-up Berhasil!',
            `Saldo baru: Rp ${response.data.saldo_baru.toLocaleString('id-ID')}`
          );
          await fetchMembers();
        } else {
          showError('Error', response.message || 'Gagal melakukan top-up');
        }
      }
    } catch (err) {
      showError('Error', err.message || 'Gagal melakukan top-up');
      console.error('Error top-up member:', err);
    }
  };

  // Handle view history
  const handleViewHistory = (member) => {
    // In a real app, you'd open a modal or navigate to a detail page
    console.log('View history for member:', member);
    alert(`Riwayat ${member.nama}:\n- Top-up: ${member.riwayat?.length || 0} transaksi\n- Penggunaan: ${member.riwayat_penggunaan?.length || 0} sesi`);
  };

  // Initial load
  useEffect(() => {
    fetchMembers();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading members...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <Button onClick={fetchMembers}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Data Member</h1>
            <p className="text-gray-600 mt-1">Kelola data member dan saldo</p>
          </div>
          
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
            
            <Button onClick={handleCreateMember}>
              <UserPlus className="w-4 h-4 mr-2" />
              Tambah Member
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Cari ID atau Nama Member..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Members Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
          {filteredMembers.map((member) => (
            <MemberCard
              key={member.id}
              member={member}
              onEdit={handleEditMember}
              onDelete={handleDeleteMember}
              onTopup={handleTopupMember}
              onViewHistory={handleViewHistory}
            />
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg mb-4">
              {searchQuery ? 'Tidak ada member yang ditemukan' : 'Belum ada member'}
            </p>
            {!searchQuery && (
              <Button onClick={handleCreateMember}>
                <UserPlus className="w-4 h-4 mr-2" />
                Tambah Member Pertama
              </Button>
            )}
          </div>
        )}

        {/* Stats */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Member</h3>
            <p className="text-3xl font-bold text-blue-600">{members.length}</p>
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Saldo</h3>
            <p className="text-3xl font-bold text-green-600">
              Rp {members.reduce((sum, m) => sum + m.saldo, 0).toLocaleString('id-ID')}
            </p>
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Member Aktif</h3>
            <p className="text-3xl font-bold text-purple-600">
              {members.filter(m => m.saldo > 0).length}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
